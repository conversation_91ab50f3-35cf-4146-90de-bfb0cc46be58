<#
.SYNOPSIS
Script para transcribir y traducir videos usando Whisper y ChatGPT.

.DESCRIPTION
Este script puede procesar videos de dos formas:
1. Descargando desde una URL (YouTube, etc.)
2. Usando un archivo local (.mp4 o .mkv)

.PARAMETER InputSource
URL del video o ruta completa al archivo local (.mp4/.mkv).
Si no se proporciona, se mostrará un menú interactivo.

.PARAMETER DestinationPath
Carpeta de destino opcional para guardar el video procesado.
Si no se proporciona, se usará la carpeta por defecto.

.EXAMPLE
.\transcribir.ps1 "https://www.youtube.com/watch?v=ejemplo"
.\transcribir.ps1 "C:\Videos\mi_video.mp4"
.\transcribir.ps1 "D:\Peliculas\pelicula.mkv"
.\transcribir.ps1 "https://www.youtube.com/watch?v=ejemplo" "C:\MisVideos\"
#>

[CmdletBinding()]
param(
    [Parameter(Position=0)]
    [string]$InputSource,

    [Parameter(Position=1)]
    [string]$DestinationPath
)
$TmpDir = (Join-Path -Path (Get-Location) -ChildPath "tmp") + "\"

# Configurar directorio de destino
if (-not [string]::IsNullOrWhiteSpace($DestinationPath)) {
    $DestinationPath = $DestinationPath.Trim().Trim('"').Trim("'")
    if (Test-Path -Path $DestinationPath) {
        $Dirdestino = $DestinationPath
        Write-Host "Usando directorio de destino personalizado: $Dirdestino" -ForegroundColor Cyan
    } else {
        Write-Host "ADVERTENCIA: El directorio de destino especificado no existe: $DestinationPath" -ForegroundColor Yellow
        Write-Host "Usando directorio por defecto." -ForegroundColor Yellow
        $Dirdestino = "\\************\video\TTS\"
    }
} else {
    $Dirdestino = "\\************\video\TTS\"
}

# Verificar si existe la carpeta tmp y crearla si no existe
if (-not (Test-Path -Path $TmpDir)) {
    Write-Host "Creando directorio temporal: $TmpDir" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $TmpDir -Force | Out-Null
}

# Función para detectar si es una URL
function Test-IsUrl {
    param([string]$InputString)
    return $InputString -match '^https?://'
}

# Función para detectar si es un archivo de video válido
function Test-IsVideoFile {
    param([string]$FilePath)
    if (-not (Test-Path -LiteralPath $FilePath)) {
        return $false
    }
    $extension = [IO.Path]::GetExtension($FilePath).ToLower()
    return $extension -in @('.mp4', '.mkv')
}

# Si viene InputSource -> detectar automáticamente si es URL o archivo
if (-not [string]::IsNullOrWhiteSpace($InputSource)) {
    $InputSource = $InputSource.Trim().Trim('"').Trim("'")

    if (Test-IsUrl $InputSource) {
        $choice = '1'
        $videoUrl = $InputSource
        Write-Host "Detectada URL: $InputSource" -ForegroundColor Cyan
    }
    elseif (Test-IsVideoFile $InputSource) {
        $choice = '2'
        $tituloVideo = $InputSource
        Write-Host "Detectado archivo local: $InputSource" -ForegroundColor Cyan
    }
    else {
        Write-Host "ERROR: El input no es una URL válida ni un archivo MP4/MKV existente: $InputSource" -ForegroundColor Red
        Write-Host "Formatos soportados: URLs (http/https) o archivos locales (.mp4, .mkv)" -ForegroundColor Yellow
        exit
    }
}
else {
    # Menú en dos líneas
    $menu = "Selecciona una opción:`n  1) Usar una URL de video`n  2) Usar archivo local (.mp4/.mkv)"
    do {
        $choice = Read-Host -Prompt $menu
    } until ($choice -in @('1','2'))
}

if ($choice -eq '1') {
    # Solo preguntar si aún no tenemos $videoUrl
    if ([string]::IsNullOrWhiteSpace($videoUrl)) {
        $videoUrl = Read-Host "Introduce la URL del video de YouTube"
    }

    $titulo = (& yt-dlp --get-filename -o '%(title)s' -- $videoUrl).Trim()
    $tituloVideo = Join-Path $TmpDir "$titulo.mp4"

    Write-Host "Descargando el video: $tituloVideo" -ForegroundColor Green
    & yt-dlp -f "bestvideo[ext=mp4]+bestaudio[ext=m4a]/mp4" `
             -P $TmpDir `
             -o "$titulo.%(ext)s" `
             -- $videoUrl
}
elseif ($choice -eq '2') {
    # Solo preguntar si aún no tenemos $tituloVideo (cuando no se pasó como parámetro)
    if ([string]::IsNullOrWhiteSpace($tituloVideo)) {
        $tituloVideo = Read-Host "Introduce la ruta completa del archivo de vídeo (.mp4/.mkv)"
        $tituloVideo = $tituloVideo.Trim().Trim('"').Trim("'")

        if (-not (Test-Path -LiteralPath $tituloVideo)) {
            Write-Host "ERROR: El archivo no existe: $tituloVideo" -ForegroundColor Red
            exit
        }

        $extension = [IO.Path]::GetExtension($tituloVideo).ToLower()
        if ($extension -notin @('.mp4', '.mkv')) {
            Write-Host "ERROR: Formato de archivo no soportado. Solo se permiten archivos .mp4 y .mkv" -ForegroundColor Red
            exit
        }
    }

    $titulo = [IO.Path]::GetFileNameWithoutExtension($tituloVideo)
    Write-Host "Usando el vídeo existente: $tituloVideo" -ForegroundColor Yellow
}
else {
    Write-Host "Opción no válida." -ForegroundColor Red
    exit
}



Write-Host "Título del vídeo: $TmpDir$titulo" -ForegroundColor Cyan

# Aquí convertimos el vídeo a MP3.

Write-Host "Convertimos el video a mp3  " -ForegroundColor Green
# Usar la ruta del video actual (puede ser del directorio temporal o archivo local)
$videoSource = if ($choice -eq '1') { "$TmpDir$titulo.mp4" } else { $tituloVideo }
ffmpeg -y -i "$videoSource" -vn -acodec libmp3lame -q:a 2 "$TmpDir$titulo.mp3"
$inAudio = "$TmpDir$titulo.mp3"

Write-Host "Convertimos el video a mp3 y separamos las cadenas de audio  " -ForegroundColor Yellow
$stem     = [IO.Path]::GetFileNameWithoutExtension($inAudio)
$trackDir = Join-Path (Join-Path $TmpDir 'htdemucs') $stem
$noVocals = Join-Path $trackDir 'no_vocals.wav'
$vocals   = Join-Path $trackDir 'vocals.wav'
python -m demucs.separate -d cuda -n htdemucs --two-stems=vocals -o "$TmpDir" "$inAudio"

Write-Host "Convertido a mp3 y separadas las cadenas de audio del fichero $vocals  " -ForegroundColor Green
# read-host "Continuamos?......."


# Aquí generamos los subtítulos.
Write-Host "Ahora generamos los subtitulos.....  "  -ForegroundColor Green
$subtitulo = $TmpDir+$titulo + ".srt"
python transcribir.py "$vocals" --srt "$subtitulo"

Write-Host "Subtitulos generados en  $subtitulo "  -ForegroundColor Green
# read-host "Continuamos?......." 


# ################################################################################################################################################################
# Una vez creado el subtítulo procedemos a traducirlo

Write-Host "Traduciendo a castellano...... "  -ForegroundColor Green
node .\chatgpt-subtitle-translator\cli\translator.mjs --to Spanish --model gpt-5-nano --stream --input "$subtitulo"
# Copiamos el resultado a \subtitle_to_speech\data
$subtituloES =  $subtitulo + ".out_Spanish.srt"
$audioES =  $subtitulo + "._Spanish.es"
# cp $subtituloES \subtitle_to_speech\data

Write-Host "Tranducido en $subtituloES"  -ForegroundColor Green
# read-host "Continuamos?......."

# Ahora convertimos los subtítulos que ya los tenemos en español a un MP3 con TTS
Write-Host "Ahora convertimos los subtítulos que ya los tenemos en español a un MP3 con TTS...... "  -ForegroundColor Green
python .\srt_to_mp3_sync.py "$subtituloES" -o "$audioES"
Write-Host "Tranducido a audio en $audioES"  -ForegroundColor Green
# read-host "Continuamos?......."

# Ahora juntamos el audio en español con el vídeo y eliminamos la cadena de audio original
Write-Host "Ahora juntamos el audio en español con el vídeo y eliminamos la cadena de audio original..... "  -ForegroundColor Green
# Usar la fuente de video correcta (temporal o archivo local)
$videoSourceFinal = if ($choice -eq '1') { "$TmpDir$titulo.mp4" } else { $tituloVideo }
$outPath = Join-Path $Dirdestino ("{0}.mp4" -f $titulo)

& ffmpeg -i $videoSourceFinal -i $audioES -i $noVocals `
  -filter_complex '[1:a]aresample=async=1,volume=1.0[a_voice];[2:a]aresample=async=1,volume=0.35[a_bg];[a_voice][a_bg]amix=inputs=2:duration=first:normalize=0[a_mix]' `
  -map 0:v:0 -map '[a_mix]' -c:v copy -c:a aac -shortest -movflags +faststart -y $outPath
