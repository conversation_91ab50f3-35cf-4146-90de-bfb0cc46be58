import argparse
from subtoaudio import SubToAudio

def convert_subtitles_to_audio(input_file, output_file):
    # Usar siempre el modelo por defecto en español
    model_name = "tts_models/es/css10/vits"
    
    sub = SubToAudio(model_name=model_name)
    subtitle_data = sub.subtitle(input_file)
    sub.convert_to_audio(sub_data=subtitle_data, output_path=output_file)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convierte subtítulos en audio en español.")
    parser.add_argument("input", help="Archivo de subtítulos (.srt o .ass)")
    parser.add_argument("output", help="Archivo de salida de audio (.mp3 o .wav)")
    args = parser.parse_args()

    convert_subtitles_to_audio(args.input, args.output)
