<#
.SYNOPSIS
Script para procesar todos los videos de una carpeta y subcarpetas usando transcribir.ps1.

.DESCRIPTION
Este script busca recursivamente archivos de video (.mp4 y .mkv) en un directorio
y sus subcarpetas, y ejecuta el script transcribir.ps1 para cada archivo encontrado.

.PARAMETER InputDirectory
Ruta completa del directorio de entrada donde buscar los archivos de video.

.PARAMETER OutputDirectory
Ruta completa del directorio de salida donde guardar los videos procesados.
Si no se proporciona, se usará el directorio por defecto: \\************\video\TTS\

.EXAMPLE
.\procesarCarpeta.ps1 "C:\Videos\Entrada"
.\procesarCarpeta.ps1 "C:\Videos\Entrada" "C:\Videos\Salida"
.\procesarCarpeta.ps1 "D:\Peliculas" "\\servidor\videos\procesados"

.NOTES
Requiere que el script transcribir.ps1 esté en el mismo directorio.
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true, Position=0)]
    [string]$InputDirectory,

    [Parameter(Mandatory=$false, Position=1)]
    [string]$OutputDirectory
)

# Función para escribir mensajes con timestamp
function Write-TimestampedMessage {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

# Limpiar y validar parámetros de entrada
$InputDirectory = $InputDirectory.Trim().Trim('"').Trim("'")

# Configurar directorio de salida
if (-not [string]::IsNullOrWhiteSpace($OutputDirectory)) {
    $OutputDirectory = $OutputDirectory.Trim().Trim('"').Trim("'")
    Write-TimestampedMessage "Usando directorio de salida personalizado: $OutputDirectory" "Cyan"
} else {
    $OutputDirectory = "\\************\video\TTS\"
    Write-TimestampedMessage "Usando directorio de salida por defecto: $OutputDirectory" "Cyan"
}

Write-TimestampedMessage "=== PROCESADOR DE CARPETA DE VIDEOS ===" "Cyan"
Write-TimestampedMessage "Directorio de entrada: $InputDirectory" "Yellow"
Write-TimestampedMessage "Directorio de salida: $OutputDirectory" "Yellow"

# Verificar si existe el directorio de entrada
if (-not (Test-Path -Path $InputDirectory -PathType Container)) {
    Write-TimestampedMessage "ERROR: El directorio de entrada no existe: $InputDirectory" "Red"
    exit 1
}

# Verificar si existe el script transcribir.ps1
$transcribirScript = Join-Path -Path (Get-Location) -ChildPath "transcribir.ps1"
if (-not (Test-Path -Path $transcribirScript)) {
    Write-TimestampedMessage "ERROR: No se encuentra el script transcribir.ps1 en el directorio actual: $transcribirScript" "Red"
    exit 1
}

# Crear directorio de salida si no existe
if (-not (Test-Path -Path $OutputDirectory)) {
    Write-TimestampedMessage "Creando directorio de salida: $OutputDirectory" "Yellow"
    try {
        New-Item -ItemType Directory -Path $OutputDirectory -Force | Out-Null
    }
    catch {
        Write-TimestampedMessage "ERROR: No se pudo crear el directorio de salida: $($_.Exception.Message)" "Red"
        exit 1
    }
}

# Buscar archivos de video recursivamente
Write-TimestampedMessage "Buscando archivos de video (.mp4, .mkv) en: $InputDirectory" "Green"

$videoFiles = Get-ChildItem -Path $InputDirectory -Recurse -File | Where-Object {
    $_.Extension.ToLower() -in @('.mp4', '.mkv')
}

if ($videoFiles.Count -eq 0) {
    Write-TimestampedMessage "No se encontraron archivos de video en el directorio especificado." "Yellow"
    exit 0
}

Write-TimestampedMessage "Encontrados $($videoFiles.Count) archivo(s) de video para procesar." "Green"

# Procesar cada archivo de video
$procesados = 0
$errores = 0

foreach ($videoFile in $videoFiles) {
    $procesados++
    $relativePath = $videoFile.FullName.Substring($InputDirectory.Length).TrimStart('\', '/')
    
    Write-TimestampedMessage "" "White"
    Write-TimestampedMessage "=== PROCESANDO ARCHIVO $procesados de $($videoFiles.Count) ===" "Cyan"
    Write-TimestampedMessage "Archivo: $relativePath" "White"
    Write-TimestampedMessage "Ruta completa: $($videoFile.FullName)" "Gray"
    
    try {
        # Ejecutar transcribir.ps1 con los parámetros correspondientes
        Write-TimestampedMessage "Ejecutando transcribir.ps1..." "Green"
        
        & $transcribirScript $videoFile.FullName $OutputDirectory
        
        if ($LASTEXITCODE -eq 0) {
            Write-TimestampedMessage "✓ Archivo procesado exitosamente: $($videoFile.Name)" "Green"
        } else {
            Write-TimestampedMessage "✗ Error al procesar el archivo: $($videoFile.Name) (Código de salida: $LASTEXITCODE)" "Red"
            $errores++
        }
    }
    catch {
        Write-TimestampedMessage "✗ Excepción al procesar el archivo: $($videoFile.Name)" "Red"
        Write-TimestampedMessage "Error: $($_.Exception.Message)" "Red"
        $errores++
    }
}

# Resumen final
Write-TimestampedMessage "" "White"
Write-TimestampedMessage "=== RESUMEN FINAL ===" "Cyan"
Write-TimestampedMessage "Total de archivos encontrados: $($videoFiles.Count)" "White"
Write-TimestampedMessage "Archivos procesados exitosamente: $($procesados - $errores)" "Green"
Write-TimestampedMessage "Archivos con errores: $errores" $(if ($errores -gt 0) { "Red" } else { "Green" })

if ($errores -eq 0) {
    Write-TimestampedMessage "¡Todos los archivos se procesaron correctamente!" "Green"
    exit 0
} else {
    Write-TimestampedMessage "Se completó el procesamiento con algunos errores." "Yellow"
    exit 1
}
