import argparse
import os
import re
import tempfile
import subprocess
from typing import List, <PERSON><PERSON>
from pydub import AudioSegment
from TTS.api import TTS
import torch

# ---------- Utilidades ----------

time_re = re.compile(r"(?P<h>\d{2}):(?P<m>\d{2}):(?P<s>\d{2}),(?P<ms>\d{3})")

def parse_srt(path: str) -> List[Tuple[float, float, str]]:
    """Devuelve [(start_sec, end_sec, text)] del SRT."""
    with open(path, "r", encoding="utf-8") as f:
        blocks = re.split(r"\r?\n\r?\n", f.read().strip())
    segments = []
    for b in blocks:
        lines = [x for x in b.splitlines() if x.strip() != ""]
        if len(lines) < 2:
            continue
        times_line = lines[0] if "-->" in lines[0] else (lines[1] if len(lines) > 1 else "")
        if "-->" not in times_line:
            continue
        start_txt, end_txt = [t.strip() for t in times_line.split("-->")]
        def to_sec(t: str) -> float:
            m = time_re.match(t)
            if not m: return 0.0
            h, mi, s, ms = map(int, (m["h"], m["m"], m["s"], m["ms"]))
            return h*3600 + mi*60 + s + ms/1000.0
        start, end = to_sec(start_txt), to_sec(end_txt)
        text_lines = lines[2:] if "-->" in lines[1] else lines[1:]
        text = " ".join(x.strip() for x in text_lines).strip()
        if text:
            segments.append((start, end, text))
    return segments

def ffprobe_duration(path: str) -> float:
    out = subprocess.check_output([
        "ffprobe","-v","error","-show_entries","format=duration",
        "-of","default=noprint_wrappers=1:nokey=1", path
    ]).decode("utf-8", errors="ignore").strip()
    return float(out)

def atempo_chain_factor(target_ratio: float) -> str:
    """Devuelve cadena de filtros atempo encadenados para cubrir ratios fuera [0.5,2.0]."""
    chain = []
    r = target_ratio
    while r > 2.0:
        chain.append("atempo=2.0")
        r /= 2.0
    while r < 0.5:
        chain.append("atempo=0.5")
        r *= 2.0
    chain.append(f"atempo={r:.6f}")
    return ",".join(chain)

def clean_text(t: str) -> str:
    """Limpieza ligera para caracteres problemáticos."""
    return "".join(ch for ch in t if ch.isalpha() or ch.isdigit() or ch in " .,;:!?()\"'¿¡-–—…/").strip()

# ---------- Flujo principal ----------

def srt_to_synced_mp3(
    srt_path: str,
    out_mp3: str = "voz_final.mp3",
    model_name: str = "tts_models/multilingual/multi-dataset/xtts_v2",
    speaker: str = "Luis Moray",
    language: str = "es"
):
    # 1) Cargar TTS
    use_gpu = torch.cuda.is_available()
    print(f"Usando modelo: {model_name} | GPU: {use_gpu}")
    tts = TTS(model_name=model_name, progress_bar=False, gpu=use_gpu)

    # 2) Parsear SRT
    segments = parse_srt(srt_path)
    if not segments:
        raise RuntimeError("No se han encontrado segmentos válidos en el SRT.")

    final_audio = AudioSegment.silent(duration=0)
    current_time_ms = 0

    tmpdir = tempfile.mkdtemp(prefix="srt_tts_")
    temp_files = []

    try:
        for i, (start, end, text) in enumerate(segments, start=1):
            target_ms = max(1, int((end - start) * 1000))
            gap = int(start * 1000) - current_time_ms
            if gap > 0:
                final_audio += AudioSegment.silent(duration=gap)
                current_time_ms += gap

            raw_text = clean_text(text)
            if not raw_text:
                final_audio += AudioSegment.silent(duration=target_ms)
                current_time_ms += target_ms
                print(f"[{i}] (vacío) -> silencio {target_ms} ms")
                continue

            seg_wav = os.path.join(tmpdir, f"seg_{i:05d}.wav")
            # XTTS necesita speaker y language explícitos
            tts.tts_to_file(
                text=raw_text,
                file_path=seg_wav,
                speaker=speaker,
                language=language
            )

            orig_dur = max(1e-3, ffprobe_duration(seg_wav))
            target_sec = target_ms / 1000.0
            atempo = orig_dur / target_sec
            filt = atempo_chain_factor(atempo)

            adj_wav = os.path.join(tmpdir, f"seg_{i:05d}_adj.wav")
            subprocess.run(
                ["ffmpeg","-y","-i",seg_wav,"-filter:a",filt,adj_wav],
                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, check=True
            )

            seg_audio = AudioSegment.from_wav(adj_wav)
            if len(seg_audio) > target_ms:
                seg_audio = seg_audio[:target_ms]
            elif len(seg_audio) < target_ms:
                seg_audio += AudioSegment.silent(duration=(target_ms - len(seg_audio)))

            final_audio += seg_audio
            current_time_ms += target_ms
            temp_files.extend([seg_wav, adj_wav])
            print(f"[{i}] {len(seg_audio)} ms | texto: {raw_text[:80]}{'...' if len(raw_text)>80 else ''}")

        final_audio.export(out_mp3, format="mp3")
        print(f"✅ Generado: {out_mp3}")

    finally:
        for f in temp_files:
            try: os.remove(f)
            except: pass
        try: os.rmdir(tmpdir)
        except: pass

# ---------- CLI ----------

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convierte SRT a MP3 con voz XTTS sincronizada a los timecodes.")
    parser.add_argument("srt", help="Ruta al fichero .srt")
    parser.add_argument("-o","--out", default="voz_final.mp3", help="Salida MP3")
    parser.add_argument("--model", default="tts_models/multilingual/multi-dataset/xtts_v2", help="Modelo Coqui TTS")
    parser.add_argument("--speaker", default="Luis Moray", help="Nombre exacto del speaker")
    parser.add_argument("--language", default="es", help="Código de idioma")
    args = parser.parse_args()

    srt_to_synced_mp3(args.srt, args.out, args.model, args.speaker, args.language)
