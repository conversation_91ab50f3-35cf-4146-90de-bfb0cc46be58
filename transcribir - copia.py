import argparse
import os
from datetime import timedelta
from faster_whisper import WhisperModel

def srt_timestamp(t):
    if t is None:
        t = 0.0
    ms = int(t * 1000)
    td = timedelta(milliseconds=ms)
    total_ms = int(td.total_seconds() * 1000)
    hh = total_ms // 3600000
    mm = (total_ms % 3600000) // 60000
    ss = (total_ms % 60000) // 1000
    mmm = total_ms % 1000
    return f"{hh:02d}:{mm:02d}:{ss:02d},{mmm:03d}"

def main():
    parser = argparse.ArgumentParser(description="Transcribir audio/vídeo con Faster-Whisper y generar .srt")
    parser.add_argument("input_file", help="Ruta del archivo de audio o vídeo")
    parser.add_argument("--model", default="medium", help="Modelo (tiny/base/small/medium/large-v3, etc.)")
    parser.add_argument("--device", default="cpu", help="cuda o cpu")
    parser.add_argument("--compute-type", default="float16", help="float16/int8/int8_float16, etc.")
    parser.add_argument("--language", default=None, help="Código idioma (es, en, etc.). Si omites, detecta.")
    parser.add_argument("--output_format", default="vtt", help="Formato del subtitulo")
    parser.add_argument("--beam-size", type=int, default=5, help="Beam size para decodificación")
    parser.add_argument("--srt", default=None, help="Ruta de salida .srt (por defecto usa el nombre del input)")
    args = parser.parse_args()

    # Salida por defecto
    if args.srt is None:
        base, _ = os.path.splitext(args.input_file)
        args.srt = base + ".srt"

    # Cargar modelo
    try:
        model = WhisperModel(args.model, device=args.device, compute_type=args.compute_type)
    except Exception:
        # fallback a CPU si falla CUDA
        model = WhisperModel(args.model, device="cuda", compute_type="int8")

    segments, info = model.transcribe(args.input_file, language=args.language, beam_size=args.beam_size)

    print(f"Idioma detectado: {info.language} (prob={info.language_probability:.3f})")
    # Escribir SRT
    count = 0
    with open(args.srt, "w", encoding="utf-8") as f:
        for i, seg in enumerate(segments, start=1):
            text = (seg.text or "").strip()
            if not text:
                continue
            start = srt_timestamp(seg.start)
            end   = srt_timestamp(seg.end)
            f.write(f"{i}\n{start} --> {end}\n{text}\n\n")
            count += 1

    print(f"SRT generado: {args.srt} ({count} segmentos)")

if __name__ == "__main__":
    main()
