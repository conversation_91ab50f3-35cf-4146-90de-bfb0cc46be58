<#
.SYNOPSIS
Script para procesar URLs de videos desde un archivo de texto usando transcribir.ps1.

.DESCRIPTION
Este script lee un archivo de texto que contiene URLs de videos (una URL por linea)
y ejecuta el script transcribir.ps1 para cada URL encontrada.

.PARAMETER InputFile
Ruta completa del archivo de texto que contiene las URLs de videos.

.PARAMETER OutputDirectory
Ruta completa del directorio de salida donde guardar los videos procesados.
Si no se proporciona, se usara el directorio por defecto: \\************\video\TTS\

.EXAMPLE
.\procesarFichero.ps1 "C:\urls.txt"
.\procesarFichero.ps1 "C:\urls.txt" "C:\Videos\Salida"
.\procesarFichero.ps1 "D:\lista_videos.txt" "\\servidor\videos\procesados"

.NOTES
Requiere que el script transcribir.ps1 este en el mismo directorio.
El archivo de texto debe contener una URL por linea.
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true, Position=0)]
    [string]$InputFile,
    
    [Parameter(Mandatory=$false, Position=1)]
    [string]$OutputDirectory
)

# Funcion para escribir mensajes con timestamp
function Write-TimestampedMessage {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

# Funcion para validar si una cadena es una URL
function Test-IsUrl {
    param([string]$InputString)
    return $InputString -match '^https?://'
}

# Limpiar y validar parametros de entrada
$InputFile = $InputFile.Trim().Trim('"').Trim("'")

# Configurar directorio de salida
if (-not [string]::IsNullOrWhiteSpace($OutputDirectory)) {
    $OutputDirectory = $OutputDirectory.Trim().Trim('"').Trim("'")
    Write-TimestampedMessage "Usando directorio de salida personalizado: $OutputDirectory" "Cyan"
} else {
    $OutputDirectory = "\\************\video\TTS\"
    Write-TimestampedMessage "Usando directorio de salida por defecto: $OutputDirectory" "Cyan"
}

Write-TimestampedMessage "=== PROCESADOR DE FICHERO DE URLS ===" "Cyan"
Write-TimestampedMessage "Archivo de entrada: $InputFile" "Yellow"
Write-TimestampedMessage "Directorio de salida: $OutputDirectory" "Yellow"

# Verificar si existe el archivo de entrada
if (-not (Test-Path -Path $InputFile -PathType Leaf)) {
    Write-TimestampedMessage "ERROR: El archivo de entrada no existe: $InputFile" "Red"
    exit 1
}

# Verificar si existe el script transcribir.ps1
$transcribirScript = Join-Path -Path (Get-Location) -ChildPath "transcribir.ps1"
if (-not (Test-Path -Path $transcribirScript)) {
    Write-TimestampedMessage "ERROR: No se encuentra el script transcribir.ps1 en el directorio actual: $transcribirScript" "Red"
    exit 1
}

# Crear directorio de salida si no existe
if (-not (Test-Path -Path $OutputDirectory)) {
    Write-TimestampedMessage "Creando directorio de salida: $OutputDirectory" "Yellow"
    try {
        New-Item -ItemType Directory -Path $OutputDirectory -Force | Out-Null
    }
    catch {
        Write-TimestampedMessage "ERROR: No se pudo crear el directorio de salida: $($_.Exception.Message)" "Red"
        exit 1
    }
}

# Leer el archivo de texto y obtener las URLs
Write-TimestampedMessage "Leyendo URLs del archivo: $InputFile" "Green"

try {
    $allLines = Get-Content -Path $InputFile -Encoding UTF8
}
catch {
    Write-TimestampedMessage "ERROR: No se pudo leer el archivo: $($_.Exception.Message)" "Red"
    exit 1
}

# Filtrar lineas vacias y obtener solo URLs validas
$urls = @()
$lineNumber = 0

foreach ($line in $allLines) {
    $lineNumber++
    $line = $line.Trim()
    
    # Saltar lineas vacias o comentarios
    if ([string]::IsNullOrWhiteSpace($line) -or $line.StartsWith("#")) {
        continue
    }
    
    # Validar si es una URL
    if (Test-IsUrl $line) {
        $urls += @{
            Url = $line
            LineNumber = $lineNumber
        }
    } else {
        Write-TimestampedMessage "ADVERTENCIA: Linea $lineNumber no es una URL valida: $line" "Yellow"
    }
}

if ($urls.Count -eq 0) {
    Write-TimestampedMessage "No se encontraron URLs validas en el archivo especificado." "Yellow"
    exit 0
}

Write-TimestampedMessage "Encontradas $($urls.Count) URL(s) valida(s) para procesar." "Green"

# Procesar cada URL
$procesados = 0
$errores = 0

foreach ($urlInfo in $urls) {
    $procesados++
    $url = $urlInfo.Url
    $lineNum = $urlInfo.LineNumber
    
    Write-TimestampedMessage "" "White"
    Write-TimestampedMessage "=== PROCESANDO URL $procesados de $($urls.Count) ===" "Cyan"
    Write-TimestampedMessage "Linea $lineNum: $url" "White"
    
    try {
        # Ejecutar transcribir.ps1 con los parametros correspondientes
        Write-TimestampedMessage "Ejecutando transcribir.ps1..." "Green"
        
        & $transcribirScript $url $OutputDirectory
        
        if ($LASTEXITCODE -eq 0) {
            Write-TimestampedMessage "URL procesada exitosamente: $url" "Green"
        } else {
            Write-TimestampedMessage "Error al procesar la URL: $url (Codigo de salida: $LASTEXITCODE)" "Red"
            $errores++
        }
    }
    catch {
        Write-TimestampedMessage "Excepcion al procesar la URL: $url" "Red"
        Write-TimestampedMessage "Error: $($_.Exception.Message)" "Red"
        $errores++
    }
}

# Resumen final
Write-TimestampedMessage "" "White"
Write-TimestampedMessage "=== RESUMEN FINAL ===" "Cyan"
Write-TimestampedMessage "Total de URLs encontradas: $($urls.Count)" "White"
Write-TimestampedMessage "URLs procesadas exitosamente: $($procesados - $errores)" "Green"
Write-TimestampedMessage "URLs con errores: $errores" $(if ($errores -gt 0) { "Red" } else { "Green" })

if ($errores -eq 0) {
    Write-TimestampedMessage "Todas las URLs se procesaron correctamente!" "Green"
    exit 0
} else {
    Write-TimestampedMessage "Se completo el procesamiento con algunos errores." "Yellow"
    exit 1
}
